package io.github.clive.luxomssystem.application

import com.alibaba.excel.EasyExcel
import io.github.clive.luxomssystem.application.converter.SupplierOrderConverter.toExcelModel
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.common.utils.PDFMerger
import io.github.clive.luxomssystem.common.utils.generateQRCodesAndMergeToPDF
import io.github.clive.luxomssystem.common.utils.plusHours
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.SupplierMainOrder
import io.github.clive.luxomssystem.domain.UserExcelTask
import io.github.clive.luxomssystem.domain.UserExcelTaskStatus
import io.github.clive.luxomssystem.domain.convertor.SupplierOrderConverter
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.domain.suborder.SubOrderPushSupplierEvent
import io.github.clive.luxomssystem.domain.supplierOrder.domainservice.SupplierOrderDomainService
import io.github.clive.luxomssystem.domain.supplierOrder.event.SupplierOrderEvent
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.facade.order.supplierorder.SupplierOrderController
import io.github.clive.luxomssystem.facade.order.supplierorder.request.CreateSupplierOrderRequest
import io.github.clive.luxomssystem.facade.order.supplierorder.request.UpdateSupplierOrderRequest
import io.github.clive.luxomssystem.facade.order.supplierorder.response.SupplierOrderExcelModel
import io.github.clive.luxomssystem.facade.order.supplierorder.response.SupplierOrderResponse
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosRemoteService
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.config.properties.WebHookType
import io.github.clive.luxomssystem.infrastructure.remote.LarkRemoteNotifyService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.*
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletResponse
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.net.URLEncoder

@Service
class SupplierOrderApplicationService(
    private val supplierOrderRepository: SupplierOrderRepository,
    private val supplierOrderDomainService: SupplierOrderDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    private val ossClient: CosInnerRemoteService,
    private val bizOssClient: CosRemoteService,
    private val userExcelTaskRepository: UserExcelTaskRepository,
    private val mergePDFMerger: PDFMerger,
    private val eventBus: ApplicationEventPublisher,
    private val supplierMainOrderRepository: SupplierMainOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val larkRemoteNotifyService: LarkRemoteNotifyService,
    private val mainOrderRepository: MainOrderRepository,
    @Value("\${system.external-base-url}")
    private val externalBaseUrl: String,
) {
    /**
     * Creates a new SupplierOrder.
     */
    @Transactional
    fun createSupplierOrder(request: CreateSupplierOrderRequest): SupplierOrderResponse {
        val supplierOrder =
            SupplierOrder(
                subOrderId = request.subOrderId,
                bizId = request.bizId,
                customerId = request.customerId,
                orderNo = request.orderNo,
            ).apply {
                recipient.apply {
                    country = request.country
                    state = request.state
                    city = request.city
                    postcode = request.postcode
                    phone = request.phone
                }
                product.apply {
                    qty = request.qty
                }
                shipping.apply {
                    channel = WaybillChannel.matchchannel(request.channel)
                        ?: throw IllegalArgumentException("无效的渠道: ${request.channel}")
                    shipMethod = request.shipMethod
                }
            }

        supplierOrderDomainService.performInitialSetup(supplierOrder)
        val savedSupplierOrder = supplierOrderRepository.saveAndFlush(supplierOrder)

//        eventPublisher.publishEvent(SupplierOrderEvent.Created(savedSupplierOrder.id))

        return SupplierOrderResponse.fromDomain(savedSupplierOrder)
    }

    /**
     * Retrieves a SupplierOrder by its ID.
     */
    fun getSupplierOrderById(id: Long): SupplierOrderResponse {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }
        return SupplierOrderResponse.fromDomain(supplierOrder)
    }

    /**
     * Updates an existing SupplierOrder.
     */
    @Transactional
    fun updateSupplierOrder(
        id: Long,
        request: UpdateSupplierOrderRequest,
    ): SupplierOrderResponse {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }

        supplierOrder.apply {
            product.qty = request.qty
            // Update other fields as necessary
        }

        supplierOrderDomainService.validateSupplierOrder(supplierOrder)
        val updatedSupplierOrder = supplierOrderRepository.save(supplierOrder)

        eventPublisher.publishEvent(SupplierOrderEvent.Updated(updatedSupplierOrder.id))

        return SupplierOrderResponse.fromDomain(updatedSupplierOrder)
    }

    /**
     * Changes the status of a SupplierOrder.
     */
    @Transactional
    fun changeStatus(
        id: Long,
        newStatus: SupplierOrderStatus,
    ): SupplierOrderResponse {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }

        supplierOrder.status = newStatus
        val updatedSupplierOrder = supplierOrderRepository.save(supplierOrder)

        eventPublisher.publishEvent(SupplierOrderEvent.StatusChanged(updatedSupplierOrder.id, newStatus.name))

        return SupplierOrderResponse.fromDomain(updatedSupplierOrder)
    }

    @Transactional(rollbackFor = [Throwable::class])
    @EventListener(MainOrderEvent.OrderNtfySupplyEvent::class)
    fun handle(event: MainOrderEvent.OrderNtfySupplyEvent) {
        val mainOrder =
            mainOrderRepository.findByIdOrNull(event.orderId) ?: throw IllegalArgumentException("订单不存在")

        mainOrder.supplierNotified = true
        mainOrderRepository.saveAndFlush(mainOrder)

        log.info { "订单通知供应链事件处理开始 | 操作: 订单通知 | 订单ID: ${event.orderId}" }
        val subOrders = subOrderRepository.findByParentIdOrderByIdAsc(event.orderId)

        if (subOrders.isEmpty()) {
            log.error { "子订单查询失败 | 操作: 订单通知 | 订单ID: ${event.orderId} | 原因: 未找到子订单" }
            throw IllegalArgumentException("未找到子订单, 无法通知采购")
        }

        log.info { "子订单查询成功 | 操作: 订单通知 | 订单ID: ${event.orderId} | 子订单数量: ${subOrders.size}" }

        // FIXME 限制先去除,等流程理通
//        val failedSubOrderCount = subOrders.count { it.status == SubOrderStatus.FAILED }

//        if (failedSubOrderCount > 0) {
//            log.error { "子订单状态异常 | 操作: 订单通知 | 订单ID: ${event.orderId} | 原因: 存在${failedSubOrderCount}个失败的子订单" }
//            throw IllegalArgumentException("存在${failedSubOrderCount}个失败的子订单, 无法通知采购")
//        }

//        val failedWaybillCount = subOrders.count { it.waybillStatus == WayBillStatus.FAILED }
//        if (failedWaybillCount > 0) {
//            log.error { "子订单状态异常 | 操作: 订单通知 | 订单ID: ${event.orderId} | 原因: 存在${failedWaybillCount}个失败的运单" }
//            throw IllegalArgumentException("存在${failedWaybillCount}个失败的运单, 无法通知采购")
//        }

        // TODO 这里文案我瞎填的

        val link =
            kotlin
                .runCatching {
                    val encodedFilename = URLEncoder.encode(mainOrder.fileName, Charsets.UTF_8)
                    "${externalBaseUrl.removeSuffix("/")}/dashboard/order/main-order?_from=lark-notify&fileName=$encodedFilename"
                }.onFailure {
                    log.warn(it) { "为订单生成通知外链失败 | 操作: 订单通知 | 订单ID: ${event.orderId}" }
                }.getOrNull()

        larkRemoteNotifyService.sendNotification(
            WebHookType.CAN_PUSH_SUPPLIER,
            LarkRemoteNotifyService.LarkMessage(
                title = "采购通知",
                content = mainOrder.fileName,
                link = link,
                atAll = true,
            ),
        )
    }

    @EventListener(SubOrderPushSupplierEvent::class)
    @Transactional(rollbackFor = [Exception::class])
    fun handle(event: SubOrderPushSupplierEvent) {
        log.info { "子订单推送供应商事件处理开始 | 操作: 子订单推送 | 子订单ID: ${event.id} | 子订单号: ${event.orderNo}" }
        val subOrder =
            subOrderRepository.findByIdOrNull(event.id) ?: run {
                log.warn { "子订单不存在 | 操作: 子订单推送 | 子订单ID: ${event.id} | 子订单号: ${event.orderNo}" }
                return
            }
        if (!subOrder.canPushSupplier()) {
            log.info { "子订单状态异常 | 操作: 子订单推送 | 子订单ID: ${event.id} | 子订单号: ${event.orderNo} | 子订单状态: ${subOrder.status}" }
            return
        }
        handle(subOrder)
    }

    @Async
    @TransactionalEventListener(MainOrderEvent.OrderPushSupplyEvent::class)
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun handle(event: MainOrderEvent.OrderPushSupplyEvent) {
        try {
            log.info { "订单推送供应链事件处理开始 | 操作: 订单推送 | 订单ID: ${event.orderId}" }
            val subOrders = subOrderRepository.findByParentIdOrderByIdAsc(event.orderId).filter { it.canPushSupplier() }

            if (subOrders.isEmpty()) {
                log.error { "子订单查询失败 | 操作: 订单推送 | 订单ID: ${event.orderId} | 原因: 未找到子订单" }
                return
            }

            log.info { "子订单查询成功 | 操作: 订单推送 | 订单ID: ${event.orderId} | 子订单数量: ${subOrders.size}" }

            subOrders.forEach { handle(it) }

            val findMainOrderAssignedSuppliers =
                supplierOrderRepository.findMainOrderAssignedSuppliers(event.orderId).filterNotNull()

            val supplierMainOrders =
                findMainOrderAssignedSuppliers
                    .map { supplierId ->
                        val firstSubOrder = subOrders.firstOrNull() ?: throw IllegalArgumentException("子订单不存在")
                        val user = UserContextHolder.user ?: throw IllegalStateException("用户未登录")
                        val userId = user.id

                        val supplier =
                            supplierRepository.findByIdOrNull(supplierId)
                                ?: throw IllegalArgumentException("供应商不存在")

                        // 获取主订单的附件信息
                        val mainOrder =
                            mainOrderRepository.findByIdOrNull(event.orderId)
                                ?: throw IllegalArgumentException("主订单不存在")

                        log.info { "供应商信息获取成功 | 操作: 订单推送 | 供应商ID: ${supplier.id}" }

                        SupplierMainOrder(
                            supplierId = supplierId,
                            supplierName = supplier.name,
                            mainOrderId = event.orderId,
                            fileName = firstSubOrder.fileName ?: "未知文件名",
                            bizId = firstSubOrder.bizId, // 提供默认值或处理异常
                            attachmentUrls = mainOrder.attachmentUrls,
                        ).apply {
                            createdByName = user.name
                            updatedByName = user.name
                            createdBy = userId
                            updatedBy = userId
                        }
                    }

            supplierMainOrderRepository.saveAll(supplierMainOrders)
            log.info { "订单推送完成 | 操作: 订单推送 | 订单ID: ${event.orderId} | 创建主订单数: ${supplierMainOrders.size}" }
        } catch (e: Exception) {
            log.error(e) { "订单推送失败 | 操作: 订单推送 | 订单ID: ${event.orderId} | 错误类型: ${e.javaClass.simpleName} | 错误信息: ${e.message}" }
        }
    }

    fun handle(subOrder: SubOrder) {
        log.info { "子订单等待推送供应事件 | 子订单ID: ${subOrder.orderNo} | 操作: 开始处理" }

        val supplierOrder = SupplierOrderConverter.fromSubOrder(subOrder)
        log.info { "子订单转换完成 | 子订单ID: ${subOrder.orderNo}  | 状态: ${supplierOrder.status}" }

        supplierOrderRepository.saveAndFlush(supplierOrder)
        log.info { "供应商订单保存成功 | 供应商订单ID: ${supplierOrder.id} | 订单号: ${supplierOrder.orderNo} | 状态: 已匹配供应商 | 操作: 发布匹配供应商事件" }
        eventBus.publishEvent(SupplierOrderEvent.MatchedSupplier(supplierOrder.id, supplierOrder.orderNo))
    }

    @EventListener(SupplierOrderEvent.MatchedSupplier::class)
    fun handle(event: SupplierOrderEvent.MatchedSupplier) {
        val orderNo = event.orderNo
        log.info { "处理供应商匹配事件 | OrderNo: $orderNo" }

        val waybills = waybillRepository.findByWaybillAccordingToOrderNo(orderNo)
        if (waybills.isEmpty()) {
            log.error { "未找到运单记录 | OrderNo: $orderNo" }
            return
        }

        if (waybills.size > 1) {
            log.error { "发现多个运单记录 | OrderNo: $orderNo | WaybillCount: ${waybills.size}" }
        }

        val waybill = waybills.first()
        if (waybill.status.onCompleted()) {
            log.info { "运单已完成 | OrderNo: $orderNo | WaybillOrderNos: ${waybill.orderNos} | WaybillId: ${waybill.id}" }

            val orderNos = waybill.orderNos.split(",").map { it.trim() }
            val supplierOrders = supplierOrderRepository.findByBizIdAndOrderNoIn(waybill.bizId, orderNos)

            log.info { "处理供应商订单 | OrderNo: $orderNo | SupplierOrderCount: ${supplierOrders.size} | BizId: ${waybill.bizId}" }
            supplierOrders.forEach {
                fillSupplierWithWayBillData(it, waybill)
            }
        } else {
            log.info { "运单未完成 | OrderNo: $orderNo | WaybillStatus: ${waybill.status}" }
        }
    }

    private fun fillSupplierWithWayBillData(
        it: SupplierOrder,
        waybill: Waybill,
    ): SupplierOrder {
        log.info { "订单运单数据填充 | 操作: 填充订单运单数据 | 订单号: ${it.orderNo} | 运单ID: ${waybill.id}" }

        it.status = SupplierOrderStatus.PRODUCING
        it.shipping.wayBillRelation = waybill.shipping.wayBillRelation
        it.failAtStatus = null
        it.errorMsg = null
        it.waybillId = waybill.id
        it.shipping.channel = waybill.shipping.channel
        it.shipping.shipMethod = waybill.shipping.shipMethod
        it.shipping.waybillLabelUrl = waybill.shipping.waybillLabelUrl

        return supplierOrderRepository.saveAndFlush(it)
    }

//    @PostConstruct
//    fun init() {
//        waybillRepository.findByMainOrderId(183538890209513507)
//            .map { handle(WaybillCompletedEvent(it.id,1, it.orderNos)) }
//    }

    @Async
    @TransactionalEventListener(WaybillCompletedEvent::class)
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun handle(event: WaybillCompletedEvent) {
        val startTime = System.currentTimeMillis()
        log.info { "事件处理开始 | 类型: WaybillCompletedEvent | 运单ID: ${event.waybillId} | 订单号列表: ${event.orderNos}" }

        try {
            waybillRepository.findByIdOrNull(event.waybillId)?.let { wayBill ->
                val orderList = event.orderNos.split(",")
                log.info { "查询订单 | 业务ID: ${wayBill.bizId} | 订单数量: ${orderList.size} | 订单号列表: ${event.orderNos}" }

                val supplierOrders =
                    supplierOrderRepository.findByBizIdAndOrderNoIn(
                        wayBill.bizId,
                        orderList,
                    )

                log.info { "订单处理 | 找到订单数: ${supplierOrders.size} | 预期订单数: ${orderList.size} | 订单号列表: ${event.orderNos}" }

                supplierOrders.forEach {
                    fillSupplierWithWayBillData(it, wayBill)
                }

                val processingTime = System.currentTimeMillis() - startTime
                log.info {
                    "事件处理完成 | 运单ID: ${event.waybillId} | 订单号列表: ${event.orderNos} | 处理状态: 成功 | 处理时间: ${processingTime}ms | 处理订单数: ${supplierOrders.size}"
                }
            } ?: run {
                log.error { "事件处理失败 | 类型: WaybillCompletedEvent | 运单ID: ${event.waybillId} | 错误原因: 运单不存在 | 订单号列表: ${event.orderNos}" }
            }
        } catch (e: Exception) {
            val processingTime = System.currentTimeMillis() - startTime
            log.error {
                "事件处理异常 | 类型: WaybillCompletedEvent | 运单ID: ${event.waybillId} | 订单号列表: ${event.orderNos} | 处理时间: ${processingTime}ms | 异常类型: ${e.javaClass.simpleName} | 异常信息: ${e.message}"
            }
            throw e
        }
    }

    fun pageQuery(req: SupplierOrderController.SupplierOrderPageRequest): PageResponse<SupplierOrderResponse> {
        val orderNos =
            if (req.orderNos != null) {
                req.orderNos!!.lines()
            } else {
                emptyList()
            }
        if (UserContextHolder.user!!.supplierId == null) {
            return supplierOrderRepository
                .pageNoSupplier(
                    UserContextHolder.user!!.bizId,
                    req.orderNo,
                    req.status,
                    req.toPageable(),
                    req.spu,
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                    req.supplierId,
                    orderNos,
                    req.country,
                    req.countryCode,
                ).map {
                    SupplierOrderResponse.fromDomain(it)
                }.toResponse()
        } else {
            return supplierOrderRepository
                .page(
                    UserContextHolder.user!!.supplierId,
                    UserContextHolder.user!!.bizId,
                    req.orderNo,
                    req.status,
                    req.toPageable(),
                    req.spu,
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                    orderNos,
                    req.country,
                    req.countryCode,
                ).map {
                    SupplierOrderResponse.fromDomain(it)
                }.toResponse()
        }
    }

    fun listSupplierOrder(wayBillRelation: String): List<SupplierOrderResponse> =
        supplierOrderRepository
            .findByBizIdAndShipping_WayBillRelation(
                bizId = UserContextHolder.user!!.bizId,
                wayBillRelation = wayBillRelation,
            ).map {
                val subOrder = subOrderRepository.findByOrderNo(it.orderNo)
                SupplierOrderResponse.fromDomain(it).apply {
                    weight = subOrder?.product?.weight
                    price = subOrder?.product?.price
                }
            }

    @Transactional(rollbackFor = [Exception::class])
    fun handleException(ids: List<Long>) {
        ids.forEach {
            handleException(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun handleException(id: Long) {
        val supplierOrder =
            supplierOrderRepository.findById(id).orElseThrow { NoSuchElementException("没有找到订单: $id") }
        if (supplierOrder.status == SupplierOrderStatus.FAILED) {
            val failedStatus = supplierOrder.failAtStatus!!
            when (failedStatus) {
                SupplierOrderStatus.CREATED -> {
                    supplierOrder.status = SupplierOrderStatus.CREATED
                    supplierOrder.failAtStatus = null
                    supplierOrder.errorMsg = null
                    supplierOrderRepository.saveAndFlush(supplierOrder)
                    eventBus.publishEvent(SupplierOrderEvent.Created(supplierOrder.id))
                }

                else -> throw IllegalArgumentException("该失败状态不支持处理 请联系开发者")
            }

            val firstSubOrder = supplierOrderRepository.findByIdOrNull(id)!!
            val user = UserContextHolder.user ?: throw IllegalStateException("用户未登录")
            val userId = user.id

            val supplierId = firstSubOrder.product.supplierId
            if (supplierId != null) {
                val supplier =
                    supplierRepository.findByIdOrNull(supplierId) ?: throw IllegalArgumentException("供应商不存在")

                log.info { "供应商信息获取成功 | 操作: 订单推送 | 供应商ID: ${supplier.id}" }
                if (supplierMainOrderRepository.findByMainOrderIdAndSupplierId(
                        firstSubOrder.mainOrderId!!,
                        supplierId,
                    ) == null
                ) {
                    log.info {
                        "供应商主订单不存在 | 操作: 供应商生成主订单 | 供应商ID: ${supplier.id} | 主订单ID: ${firstSubOrder.mainOrderId} | orderNo: ${firstSubOrder.orderNo} |"
                    }

                    // 获取主订单的附件信息
                    val originalMainOrder =
                        mainOrderRepository.findByIdOrNull(firstSubOrder.mainOrderId!!)
                            ?: throw IllegalArgumentException("主订单不存在")

                    val mainOrder =
                        SupplierMainOrder(
                            supplierId = supplierId,
                            supplierName = supplier.name,
                            mainOrderId = firstSubOrder.mainOrderId!!,
                            fileName = firstSubOrder.fileName ?: "未知文件名",
                            bizId = firstSubOrder.bizId, // 提供默认值或处理异常
                            attachmentUrls = originalMainOrder.attachmentUrls,
                        ).apply {
                            createdByName = user.name
                            updatedByName = user.name
                            createdBy = userId
                            updatedBy = userId
                        }
                    supplierMainOrderRepository.saveAndFlush(mainOrder)
                }
            }
        } else {
            throw IllegalArgumentException("订单状态不是失败状态")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancel(id: Long) {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }
        if (supplierOrder.status == SupplierOrderStatus.CREATED || supplierOrder.status == SupplierOrderStatus.MATCHED_SUPPLY) {
            if (supplierOrder.createdAt!!.plusHours(12) < System.currentTimeMillis()) {
                throw IllegalArgumentException("订单创建时间超过12小时 无法取消")
            }
            supplierOrder.status = SupplierOrderStatus.CANCELLED
            supplierOrderRepository.saveAndFlush(supplierOrder)
        } else {
            throw IllegalArgumentException("订单状态不是创建状态或者匹配到供应商状态 无法取消")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun complete(id: Long) {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }
        if (supplierOrder.status == SupplierOrderStatus.PRODUCING) {
            supplierOrder.status = SupplierOrderStatus.PRODUCT_COMPLETED
            supplierOrderRepository.saveAndFlush(supplierOrder)
        } else {
            throw IllegalArgumentException("订单状态不是生产中状态 无法完成")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun delivery(id: Long) {
        val supplierOrder =
            supplierOrderRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierOrder not found with id: $id") }
        if (supplierOrder.status == SupplierOrderStatus.PRODUCT_COMPLETED) {
            supplierOrder.status = SupplierOrderStatus.SHIPPED
            supplierOrderRepository.saveAndFlush(supplierOrder)
        } else {
            throw IllegalArgumentException("订单状态不是生产完成状态 无法发货")
        }
    }

    fun save(
        fileName: String,
        ids: List<Long>,
    ): Long {
        val nextId = nextId()
        userExcelTaskRepository.saveAndFlush(
            UserExcelTask().apply {
                id = nextId
                type = "供应商订单"
                this.fileName = fileName
                bizId = UserContextHolder.user!!.bizId
                userId = UserContextHolder.user!!.id
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            },
        )
        return nextId
    }

    fun exportContent(
        fileName: String,
        taskId: Long,
        ids: List<Long>,
        bizId: Long,
    ) {
        log.info { "开始导出供应商订单，fileName: $fileName, taskId: $taskId, ids: $ids, bizId: $bizId" }
        try {
            val supplierOrders = supplierOrderRepository.findAllById(ids)
            log.info { "查询到 ${supplierOrders.size} 个供应商订单" }

            val keys =
                runBlocking {
                    log.info { "开始获取文件路径列表" }
                    bizOssClient
                        .listFilesInDirectoriesConcurrently(
                            supplierOrders.mapNotNull { it.fileOssPath },
                            bizId,
                        ).also {
                            log.info { "获取到 ${it.size} 个文件路径" }
                        }
                }
            var url: String? = null

            if (keys.isNotEmpty()) {
                log.info { "开始打包图片文件" }
                val job =
                    bizOssClient.packageImagesUseKeysFromCOS(
                        bizId,
                        randomString(32),
                        keys,
                        bizId.toString(),
                    )
                runBlocking {
                    val timeoutMillis = 60000L
                    log.info { "等待打包任务完成，超时时间: ${timeoutMillis}ms" }
                    withTimeoutOrNull(timeoutMillis) {
                        while (url == null) {
                            url = bizOssClient.queryFileProcessJob(bizId, job)
                            delay(5000)
                        }
                    }
                    if (url == null) {
                        log.error { "打包任务超时" }
                        throw RuntimeException("打包任务超时")
                    }
                    log.info { "打包任务完成，获取到下载URL" }
                }
            }

            log.info { "开始生成Excel文件" }
            val out = ByteArrayOutputStream()
            EasyExcel
                .write(out, SupplierOrderExcelModel::class.java)
                .sheet("供应商订单")
                .doWrite(
                    supplierOrders
                        .map {
                            val wayBill =
                                it.waybillId?.let {
                                    waybillRepository.findByIdOrNull(it)
                                }
                            it.toExcelModel(wayBill)
                        }.groupBy { it.waybillNo }
                        .toSortedMap(compareBy { it })
                        .flatMap { (_, group) -> group },
                )

            val excelFileName = "supplier_orders_${fileName}_${System.currentTimeMillis()}.xlsx"
            log.info { "开始上传Excel文件: $excelFileName" }
            val excelUrl =
                ossClient.uploadFile(
                    bizId,
                    "/excel/$excelFileName",
                    ByteArrayInputStream(out.toByteArray()),
                )
            log.info { "Excel文件上传完成，URL: $excelUrl" }

            var wayBillPdfMergeUrl: String? = null
            runBlocking {
                val urls = supplierOrders.mapNotNull { it?.shipping?.waybillLabelUrl }.filter { it.isNotBlank() }
                log.info { "开始处理运单PDF，找到 ${urls.size} 个运单URL" }
                if (urls.isEmpty()) {
                    log.info { "没有运单需要处理" }
                } else if (urls.size == 1) {
                    wayBillPdfMergeUrl = urls[0]
                    log.info { "只有一个运单，直接使用原URL" }
                } else {
                    log.info { "开始合并多个运单PDF" }
                    wayBillPdfMergeUrl =
                        mergePDFMerger.mergePDFsAndUpload(
                            urls,
                            bizId,
                            "/supplier-order/excel/merge-pdf/$bizId/$taskId/$fileName.pdf",
                        )
                    log.info { "运单PDF合并完成，URL: $wayBillPdfMergeUrl" }
                }
            }

            userExcelTaskRepository.findByIdOrNull(taskId)?.let {
                log.info { "更新导出任务状态为成功，taskId: $taskId" }
                it.zipUrl = url ?: ""
                it.fileUrl = excelUrl
                it.wayBillPdfMergeUrl = wayBillPdfMergeUrl
                it.status = UserExcelTaskStatus.SUCCESS
                it.updatedAt = System.currentTimeMillis()
                userExcelTaskRepository.saveAndFlush(it)
            }
        } catch (e: Exception) {
            log.error(e) { "导出供应商订单失败，taskId: $taskId" }
            userExcelTaskRepository.findByIdOrNull(taskId)?.let {
                log.info { "更新导出任务状态为失败，taskId: $taskId" }
                it.status = UserExcelTaskStatus.FAILED
                it.updatedAt = System.currentTimeMillis()
                userExcelTaskRepository.saveAndFlush(it)
            }
        }
    }

    private fun randomString(length: Int): String {
        val allowedChars = ('A'..'Z') + ('a'..'z') + ('0'..'9')
        return (1..length)
            .map { allowedChars.random() }
            .joinToString("")
    }

    fun pageQueryExcelTask(req: PageReq): PageResponse<UserExcelTask> =
        userExcelTaskRepository.pageQuery(req.toPageable(), UserContextHolder.user!!.id).toResponse()

    fun listSupplierOrderByMainOrderId(mainOrderId: Long): List<SupplierOrderResponse> =
        supplierOrderRepository.findByMainOrderId(mainOrderId).map {
            SupplierOrderResponse.fromDomain(it)
        }

    fun failedPage(req: SupplierOrderController.SupplierOrderPageRequest): Any {
        if (UserContextHolder.user!!.supplierId == null) {
            return supplierOrderRepository
                .pageNoSupplier(
                    UserContextHolder.user!!.bizId,
                    req.orderNo,
                    listOf(SupplierOrderStatus.FAILED),
                    req.toPageable(),
                    req.spu,
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                    supplierId = req.supplierId,
                    orderNos = emptyList(),
                    country = req.country,
                    countryCode = req.countryCode,
                ).map {
                    SupplierOrderResponse.fromDomain(it)
                }.toResponse()
        } else {
            return supplierOrderRepository
                .page(
                    UserContextHolder.user!!.supplierId,
                    UserContextHolder.user!!.bizId,
                    req.orderNo,
                    listOf(SupplierOrderStatus.FAILED),
                    req.toPageable(),
                    req.spu,
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                    orderNos = emptyList(),
                    country = req.country,
                    countryCode = req.countryCode,
                ).map {
                    SupplierOrderResponse.fromDomain(it)
                }.toResponse()
        }
    }

    fun exportLabel(
        fileName: String,
        ids: List<Long>,
        response: HttpServletResponse,
    ) {
        val supplierOrders = supplierOrderRepository.findAllById(ids)
        val contentList =
            supplierOrders.map {
                "${it.orderNo}-${it.product.skuCode()}" to "${it.orderNo}-${it.product.skuCode()}"
            }

        val pdfBytes = generateQRCodesAndMergeToPDF(contentList)

        response.contentType = MediaType.APPLICATION_PDF.type
        response.characterEncoding = "utf-8"
        response.setHeader("Content-disposition", "attachment;filename=qrcodes.pdf")
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition")

        response.outputStream.write(pdfBytes)
    }

    @Transactional
    fun stopPrint(
        orderNos: List<String>,
        warning: String,
    ) {
        log.info {
            """停止打印运单操作 | Action: stopPrint 
        | OrderIds: ${orderNos.joinToString(",")} 
        | Warning: $warning
        | BatchSize: ${orderNos.size}
            """.trimMargin()
        }

        val orders = supplierOrderRepository.findByOrderNoIn(orderNos)

        if (orders.size != orderNos.size) {
            val foundIds = orders.map { it.orderNo }.toSet()
            val missingIds = orderNos.filterNot { foundIds.contains(it) }
            throw RuntimeException("Orders not found with IDs: ${missingIds.joinToString(",")}, 请先把这些单子推送给供应商")
        }

        orders.forEach {
            it.canPrintWayBillPdf = false
            it.stopPrintWayBillWarning = warning
            supplierOrderRepository.saveAndFlush(it)
        }
    }

    @Transactional
    fun cancelStopPrint(orderNos: List<String>) {
        log.info {
            """取消停止打印运单操作 | Action: cancelStopPrint 
        | OrderIds: ${orderNos.joinToString(",")} 
        | BatchSize: ${orderNos.size}
            """.trimMargin()
        }

        val orders = supplierOrderRepository.findByOrderNoIn(orderNos)

        orders.forEach {
            it.canPrintWayBillPdf = true
            it.stopPrintWayBillWarning = ""
            supplierOrderRepository.saveAndFlush(it)
        }
    }

    @Transactional
    fun completeSupplierOrder(orderNos: List<String>) {
        log.info {
            """供应商订单批量状态更新 | 操作人: 系统/未指明
          |OrderNos: ${orderNos.joinToString(",")}
          |操作类型: 完成生产状态变更
          |Key-处理单数: ${orderNos.size}
            """.trimMargin()
        }
        val supplierOrders = supplierOrderRepository.findByOrderNoIn(orderNos)
        supplierOrders.forEach {
            it.status = SupplierOrderStatus.SHIPPED
        }
        supplierOrderRepository.saveAllAndFlush(supplierOrders)
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
